package api

import (
	"net/http"

	"github.com/sirupsen/logrus"

	authRest "github.com/JosueDiazC/schedhold-backend/internal/modules/auth/api/rest"
	clientRest "github.com/JosueDiazC/schedhold-backend/internal/modules/client/api/rest"
	personRest "github.com/JosueDiazC/schedhold-backend/internal/modules/person/api/rest"
	scheduleRest "github.com/JosueDiazC/schedhold-backend/internal/modules/schedule/api/rest"
	sessionRest "github.com/JosueDiazC/schedhold-backend/internal/modules/session/api/rest"
	userRest "github.com/JosueDiazC/schedhold-backend/internal/modules/user/api/rest"
	workerRest "github.com/JosueDiazC/schedhold-backend/internal/modules/worker/api/rest"

	restMdlwr "github.com/JosueDiazC/schedhold-backend/internal/services/middleware/rest"
	restUtils "github.com/JosueDiazC/schedhold-backend/internal/utils/rest"
)

type RouteConfig interface {
	SetupRoutes()
}

type routeConfig struct {
	http            *http.ServeMux
	log             *logrus.Logger
	middlware       restMdlwr.HTTPMiddleware
	userHandler     userRest.UserHandler
	authHandler     authRest.AuthHandler
	personHandler   personRest.PersonHandler
	workerHandler   workerRest.WorkerHandler
	clientHandler   clientRest.ClientHandler
	scheduleHandler scheduleRest.ScheduleHandler
	sessionHandler  sessionRest.SessionHandler
}

// SetupRoutes implements RouteConfig.
func (r *routeConfig) SetupRoutes() {
	routes := http.NewServeMux()
	baseMiddleware := restUtils.ComposeHMiddleware(
		r.middlware.Cors,
		r.middlware.CorrelationID,
		r.middlware.Logging,
		r.middlware.Client,
	)

	authMiddleware := restUtils.ComposeHFMiddleware(
		r.middlware.Authenticated,
	)

	r.http.Handle("/", baseMiddleware(routes))

	routes.HandleFunc("POST /api/v1/users", authMiddleware(r.userHandler.Create))
	routes.HandleFunc("PUT /api/v1/users", authMiddleware(r.userHandler.Update))
	routes.HandleFunc("GET /api/v1/users/{id}", authMiddleware(r.userHandler.GetById))
	routes.HandleFunc("GET /api/v1/users", authMiddleware(r.userHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/users/{id}", authMiddleware(r.userHandler.Delete))

	routes.HandleFunc("POST /api/v1/auth/login", r.authHandler.Login)
	routes.HandleFunc("POST /api/v1/auth/logout", r.authHandler.Logout)
	routes.HandleFunc("GET /api/v1/auth/is_logged_in", authMiddleware(r.authHandler.IsLoggedIn))

	routes.HandleFunc("POST /api/v1/persons", authMiddleware(r.personHandler.Create))
	routes.HandleFunc("PUT /api/v1/persons/{id}", authMiddleware(r.personHandler.Update))
	routes.HandleFunc("GET /api/v1/persons/{id}", authMiddleware(r.personHandler.GetById))
	routes.HandleFunc("GET /api/v1/persons", authMiddleware(r.personHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/persons/{id}", authMiddleware(r.personHandler.Delete))

	routes.HandleFunc("POST /api/v1/workers", authMiddleware(r.workerHandler.Create))
	routes.HandleFunc("PUT /api/v1/workers", authMiddleware(r.workerHandler.Update))
	routes.HandleFunc("GET /api/v1/workers/{id}", authMiddleware(r.workerHandler.GetById))
	routes.HandleFunc("GET /api/v1/workers", authMiddleware(r.workerHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/workers/{id}", authMiddleware(r.workerHandler.Delete))

	// Client routes
	routes.HandleFunc("POST /api/v1/clients", authMiddleware(r.clientHandler.Create))
	routes.HandleFunc("PUT /api/v1/clients/{id}", authMiddleware(r.clientHandler.Update))
	routes.HandleFunc("GET /api/v1/clients/{id}", authMiddleware(r.clientHandler.GetById))
	routes.HandleFunc("GET /api/v1/clients", authMiddleware(r.clientHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/clients/{id}", authMiddleware(r.clientHandler.Delete))

	routes.HandleFunc("POST /api/v1/clients/public-link", authMiddleware(r.clientHandler.CreatePublicLink))
	routes.HandleFunc("PUT /api/v1/clients/public-link", authMiddleware(r.clientHandler.UpdatePublicLink))
	routes.HandleFunc("GET /api/v1/clients/public-link/{url}", r.clientHandler.GetClientLink)
	routes.HandleFunc("DELETE /api/v1/clients/{id}/public-link", authMiddleware(r.clientHandler.DeletePublicLink))
	routes.HandleFunc("GET /api/v1/clients/public-link/generate", authMiddleware(r.clientHandler.GeneratePublicLink))
	routes.HandleFunc("GET /api/v1/clients/public-link/get-by-url/{url}", authMiddleware(r.clientHandler.GetPublicClientLinkByURL))

	// Schedule routes
	routes.HandleFunc("POST /api/v1/schedules", authMiddleware(r.scheduleHandler.Create))
	routes.HandleFunc("PUT /api/v1/schedules/{id}", authMiddleware(r.scheduleHandler.Update))
	routes.HandleFunc("GET /api/v1/schedules/{id}", authMiddleware(r.scheduleHandler.GetById))
	routes.HandleFunc("GET /api/v1/schedules", authMiddleware(r.scheduleHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/schedules/{id}", authMiddleware(r.scheduleHandler.Delete))

	// Session routes
	routes.HandleFunc("POST /api/v1/sessions", authMiddleware(r.sessionHandler.Create))
	routes.HandleFunc("POST /api/v1/sessions/bulk", authMiddleware(r.sessionHandler.CreateMany))
	routes.HandleFunc("PUT /api/v1/sessions/{id}", authMiddleware(r.sessionHandler.Update))
	routes.HandleFunc("GET /api/v1/sessions/{id}", authMiddleware(r.sessionHandler.GetById))
	routes.HandleFunc("GET /api/v1/sessions", authMiddleware(r.sessionHandler.GetAll))
	routes.HandleFunc("GET /api/v1/sessions/by-client-and-turn", authMiddleware(r.sessionHandler.GetByClientAndTurn))
	routes.HandleFunc("GET /api/v1/sessions/by-worker-and-turn", authMiddleware(r.sessionHandler.GetByWorkerAndTurn))
	routes.HandleFunc("DELETE /api/v1/sessions/{id}", authMiddleware(r.sessionHandler.Delete))
	routes.HandleFunc("DELETE /api/v1/sessions/bulk", authMiddleware(r.sessionHandler.DeleteMany))
}

func NewRouteConfig(
	http *http.ServeMux,
	log *logrus.Logger,
	middleware restMdlwr.HTTPMiddleware,
	userHandler userRest.UserHandler,
	authHandler authRest.AuthHandler,
	personHandler personRest.PersonHandler,
	workerHandler workerRest.WorkerHandler,
	clientHandler clientRest.ClientHandler,
	scheduleHandler scheduleRest.ScheduleHandler,
	sessionHandler sessionRest.SessionHandler,
) RouteConfig {
	return &routeConfig{
		http:            http,
		log:             log,
		middlware:       middleware,
		userHandler:     userHandler,
		authHandler:     authHandler,
		personHandler:   personHandler,
		workerHandler:   workerHandler,
		clientHandler:   clientHandler,
		scheduleHandler: scheduleHandler,
		sessionHandler:  sessionHandler,
	}
}
