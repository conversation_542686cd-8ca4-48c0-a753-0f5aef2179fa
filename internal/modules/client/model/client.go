package model

import (
	"time"

	personModel "github.com/JosueDiazC/schedhold-backend/internal/modules/person/model"
)

type Client struct {
	ID         string
	Person     *personModel.Person
	PublicLink *string // URL from public_client_links table (only one per client)
	CreatedAt  *time.Time
	UpdatedAt  *time.Time
	DeletedAt  *time.Time
}

type ClientCreate struct {
	PersonCreate personModel.PersonCreate
}

type ClientUpdate struct {
	ID           string
	PersonUpdate personModel.PersonUpdate
}
