package model

import "github.com/JosueDiazC/schedhold-backend/internal/utils"

const (
	ClientNotFoundCode utils.ErrCode = utils.ClientCode + iota
	ConflictPersonIDCode
	InvalidUrlCode
)

func ClientNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ClientNotFoundCode, message, err, details)
}

func ConflictPersonIDf(message string, err error, details any) utils.AppErr {
	return utils.NewError(ConflictPersonIDCode, message, err, details)
}

func InvalidUrlf(message string, err error, details any) utils.AppErr {
	return utils.NewError(InvalidUrlCode, message, err, details)
}
