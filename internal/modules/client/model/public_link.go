package model

import "time"

type Public<PERSON>lientLink struct {
	ID         string
	ScheduleID string
	WorkerIDs  []string
	ClientID   string
	URL        string
	CreatedAt  *time.Time
	UpdatedAt  *time.Time
	DeletedAt  *time.Time
}

type PublicClientLinkCreate struct {
	ID         string
	ScheduleID string
	WorkerIDs  []string
	ClientID   string
	URL        string
}

type Public<PERSON>lient<PERSON>inkUpdate struct {
	ID         string
	ScheduleID string
	WorkerIDs  []string
	ClientID   string
	URL        string
}

// ClientLink represents the response for GetClientLink method
type ClientLink struct {
	Sessions []SessionInfo
	Workers  []WorkerInfo
	Client   *ClientInfo
	Schedule *SheduleInfo
}

type SheduleInfo struct {
	ID              string
	Name            string
	SessionDuration int
	BreakDuration   int
	Turns           []TurnInfo
}

type ClientInfo struct {
	ID             string
	Name           string
	FatherLastName string
	MotherLastName string
}

// SessionInfo represents session information without circular dependencies
type SessionInfo struct {
	ID         string
	IsAssigned bool
	WorkerID   string
	TurnID     string
	Day        int
	Time       int
}

// TurnInfo represents turn information without circular dependencies
type TurnInfo struct {
	ID        string
	Name      string
	StartTime int
	EndTime   int
}

// WorkerInfo represents worker information with only ID and name
type WorkerInfo struct {
	ID             string
	Name           string
	FatherLastName string
	MotherLastName string
}
