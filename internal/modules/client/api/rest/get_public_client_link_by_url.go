package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/rest"
)

type PublicClientLinkResponse struct {
	ID         string     `json:"id"`
	ScheduleID string     `json:"schedule_id"`
	WorkerIDs  []string   `json:"worker_ids"`
	ClientID   string     `json:"client_id"`
	URL        string     `json:"url"`
	CreatedAt  *time.Time `json:"created_at"`
	UpdatedAt  *time.Time `json:"updated_at"`
	DeletedAt  *time.Time `json:"deleted_at"`
}

func publicClientLinkModelToResponse(link model.PublicClientLink) PublicClientLinkResponse {
	return PublicClientLinkResponse{
		ID:         link.ID,
		ScheduleID: link.ScheduleID,
		WorkerIDs:  link.WorkerIDs,
		ClientID:   link.ClientID,
		URL:        link.URL,
		CreatedAt:  link.CreatedAt,
		UpdatedAt:  link.UpdatedAt,
		DeletedAt:  link.DeletedAt,
	}
}

// GetPublicClientLinkByURL implements ClientHandler.
func (c *clientHandler) GetPublicClientLinkByURL(w http.ResponseWriter, r *http.Request) {
	url := r.PathValue("url")
	ctx := r.Context()

	if url == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("url path parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	link, err := c.useCase.GetPublicClientLinkByURL(ctx, url)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get public client link by URL")
		return
	}

	rest.SuccessDResponse(w, r, publicClientLinkModelToResponse(*link), http.StatusOK)
}
