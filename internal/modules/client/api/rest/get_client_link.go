package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/rest"
)

type clientLinkResponse struct {
	Sessions []sessionInfo `json:"sessions"`
	Schedule *scheduleInfo `json:"schedule"`
	Workers  []workerInfo  `json:"workers"`
	Client   *clientInfo   `json:"client"`
}

type scheduleInfo struct {
	ID              string     `json:"id"`
	Name            string     `json:"name"`
	SessionDuration int        `json:"session_duration"`
	BreakDuration   int        `json:"break_duration"`
	Turns           []turnInfo `json:"turns"`
}

type clientInfo struct {
	ID             string `json:"id"`
	Name           string `json:"name"`
	FatherLastName string `json:"father_last_name"`
	MotherLastName string `json:"mother_last_name"`
}

type sessionInfo struct {
	ID         string     `json:"id"`
	IsAssigned bool       `json:"is_assigned"`
	WorkerID   string     `json:"worker_id"`
	TurnID     string     `json:"turn_id"`
	Day        int        `json:"day"`
	Time       int        `json:"time"`
	CreatedAt  *time.Time `json:"created_at"`
	UpdatedAt  *time.Time `json:"updated_at"`
	DeletedAt  *time.Time `json:"deleted_at"`
}

type turnInfo struct {
	ID        string     `json:"id"`
	Name      string     `json:"name"`
	StartTime int        `json:"start_time"`
	EndTime   int        `json:"end_time"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at"`
}

type workerInfo struct {
	ID             string `json:"id"`
	Name           string `json:"name"`
	FatherLastName string `json:"father_last_name"`
	MotherLastName string `json:"mother_last_name"`
}

func clientLinkModelToResponse(link model.ClientLink) clientLinkResponse {
	sessions := make([]sessionInfo, len(link.Sessions))
	for i, session := range link.Sessions {
		sessions[i] = sessionInfo{
			ID:         session.ID,
			WorkerID:   session.WorkerID,
			TurnID:     session.TurnID,
			Day:        session.Day,
			Time:       session.Time,
			IsAssigned: session.IsAssigned,
		}
	}

	turns := make([]turnInfo, len(link.Schedule.Turns))
	for i, turn := range link.Schedule.Turns {
		turns[i] = turnInfo{
			ID:        turn.ID,
			Name:      turn.Name,
			StartTime: turn.StartTime,
			EndTime:   turn.EndTime,
		}
	}

	workers := make([]workerInfo, len(link.Workers))
	for i, worker := range link.Workers {
		workers[i] = workerInfo{
			ID:             worker.ID,
			Name:           worker.Name,
			FatherLastName: worker.FatherLastName,
			MotherLastName: worker.MotherLastName,
		}
	}

	client := &clientInfo{
		ID:             link.Client.ID,
		Name:           link.Client.Name,
		FatherLastName: link.Client.FatherLastName,
		MotherLastName: link.Client.MotherLastName,
	}

	return clientLinkResponse{
		Sessions: sessions,
		Schedule: &scheduleInfo{
			ID:              link.Schedule.ID,
			Name:            link.Schedule.Name,
			SessionDuration: link.Schedule.SessionDuration,
			BreakDuration:   link.Schedule.BreakDuration,
			Turns:           turns,
		},
		Workers: workers,
		Client:  client,
	}
}

// GetClientLink implements ClientHandler.
func (c *clientHandler) GetClientLink(w http.ResponseWriter, r *http.Request) {
	url := r.PathValue("url")
	ctx := r.Context()

	if url == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("url path parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	response, err := c.useCase.GetClientLink(ctx, url)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to get client link")
		return
	}

	rest.SuccessDResponse(w, r, clientLinkModelToResponse(*response), http.StatusOK)
}
