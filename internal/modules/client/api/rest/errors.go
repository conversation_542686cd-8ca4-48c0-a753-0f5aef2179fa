package rest

import (
	"net/http"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	personRest "github.com/JosueDiazC/schedhold-backend/internal/modules/person/api/rest"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.ClientNotFoundCode:   http.StatusNotFound,
	model.ConflictPersonIDCode: http.StatusConflict,
	model.InvalidUrlCode:       http.StatusBadRequest,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers, personRest.ErrorHandlers)
}
