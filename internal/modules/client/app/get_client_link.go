package app

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
)

// GetClientLink implements model.ClientUsecase.
// This method returns all sessions registered on turn id and workers that have sessions with the client.
// It's similar to get all sessions method but filtered by turn and workers from public_client_links table.
func (c *clientUsecase) GetClientLink(ctx context.Context, url string) (*model.ClientLink, error) {
	return c.repo.GetClientLink(ctx, url)
}
