package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (c *clientPostgreRepo) GetClientLink(ctx context.Context, url string) (*model.ClientLink, error) {
	var sessions []model.SessionInfo
	var turns []model.TurnInfo
	var workers []model.WorkerInfo
	var client *model.ClientInfo
	var schedule model.SheduleInfo

	err := pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Query to get all sessions of a client on the public_client_links schedule_id
		// Join sessions with turns to get schedule_id, then filter by public_client_links schedule_id and workers
		sessionsQuery := `
            SELECT s.id, s.client_id, s.worker_id, s.turn_id, s.day, s.time, pcl.client_id
            FROM sessions s
            JO<PERSON> turns t ON s.turn_id = t.id AND t.deleted_at IS NULL
            JOIN public_client_links pcl ON t.schedule_id = pcl.schedule_id
                AND s.worker_id = ANY(pcl.worker_ids)
                AND pcl.deleted_at IS NULL
            WHERE pcl.url = $1 AND s.deleted_at IS NULL
            ORDER BY s.day, s.time
        `

		rows, err := conn.Query(ctx, sessionsQuery, url)
		if err != nil {
			return utils.InternalErrorf("failed to query sessions for client link", err, nil)
		}
		defer rows.Close()

		var currentClientId string

		for rows.Next() {
			var session model.SessionInfo
			var clientId *string

			err := rows.Scan(
				&session.ID,
				&clientId,
				&session.WorkerID,
				&session.TurnID,
				&session.Day,
				&session.Time,
				&currentClientId,
			)

			if clientId == nil {
				session.IsAssigned = false
			} else {
				session.IsAssigned = *clientId == currentClientId
			}

			if err != nil {
				return utils.InternalErrorf("failed to scan session", err, nil)
			}

			sessions = append(sessions, session)
		}

		if len(sessions) == 0 {
			return model.InvalidUrlf(
				fmt.Sprintf("public client link with url: %s not found", url),
				fmt.Errorf("public client link not found"),
				nil,
			)
		}

		// Query to get all turns of the schedule from the public_client_links
		turnsQuery := `
            SELECT t.id, t.name, t.start_time, t.end_time
            FROM turns t
            JOIN public_client_links pcl ON t.schedule_id = pcl.schedule_id
                AND pcl.deleted_at IS NULL
            WHERE pcl.url = $1 AND t.deleted_at IS NULL
            ORDER BY t.start_time
        `

		turnsRows, err := conn.Query(ctx, turnsQuery, url)
		if err != nil {
			return utils.InternalErrorf("failed to query turns for client link", err, nil)
		}
		defer turnsRows.Close()

		for turnsRows.Next() {
			var turn model.TurnInfo

			err := turnsRows.Scan(
				&turn.ID,
				&turn.Name,
				&turn.StartTime,
				&turn.EndTime,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan turn", err, nil)
			}

			turns = append(turns, turn)
		}

		// Query to get worker names from the public_client_links worker_ids
		workersQuery := `
            SELECT w.id, p.name, p.father_last_name, p.mother_last_name
            FROM workers w
            JOIN persons p ON w.person_id = p.id AND p.deleted_at IS NULL
            JOIN public_client_links pcl ON w.id = ANY(pcl.worker_ids)
                AND pcl.deleted_at IS NULL
            WHERE pcl.url = $1 AND w.deleted_at IS NULL
            ORDER BY p.name
        `

		workersRows, err := conn.Query(ctx, workersQuery, url)
		if err != nil {
			return utils.InternalErrorf("failed to query workers for client link", err, nil)
		}
		defer workersRows.Close()

		for workersRows.Next() {
			var worker model.WorkerInfo

			err := workersRows.Scan(
				&worker.ID,
				&worker.Name,
				&worker.FatherLastName,
				&worker.MotherLastName,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan worker", err, nil)
			}

			workers = append(workers, worker)
		}

		clientRow, err := c.GetByProp(ctx, "id", currentClientId)
		if err != nil {
			return utils.InternalErrorf("failed to get client", err, nil)
		}

		client = &model.ClientInfo{
			ID:             clientRow.ID,
			Name:           clientRow.Person.Name,
			FatherLastName: clientRow.Person.FatherLastName,
			MotherLastName: clientRow.Person.MotherLastName,
		}

		scheduleQuery := `SELECT s.id, s.name, s.session_duration, s.break_duration
			FROM schedules s
			JOIN public_client_links pcl ON s.id = pcl.schedule_id AND pcl.deleted_at IS NULL
			WHERE pcl.url = $1 AND s.deleted_at IS NULL
		`
		scheduleRow := conn.QueryRow(ctx, scheduleQuery, url)
		err = scheduleRow.Scan(
			&schedule.ID,
			&schedule.Name,
			&schedule.SessionDuration,
			&schedule.BreakDuration,
		)
		if err != nil {
			return utils.InternalErrorf("failed to scan schedule", err, nil)
		}

		schedule.Turns = make([]model.TurnInfo, len(turns))
		for i, turn := range turns {
			schedule.Turns[i] = turn
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	response := &model.ClientLink{
		Sessions: sessions,
		Schedule: &schedule,
		Workers:  workers,
		Client:   client,
	}

	return response, nil
}
